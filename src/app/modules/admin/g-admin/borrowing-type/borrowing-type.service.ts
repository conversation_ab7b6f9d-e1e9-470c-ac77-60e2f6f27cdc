import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { environment } from 'environments/environment';
import { Observable, of, switchMap } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class BorrowingTypeService {

    private _http = inject(HttpClient);

    constructor() { }

    getDataPage(
        dataTablesParameters: any
    ): Observable<DataTablesResponse> {
        return this._http
            .post(
                environment.API_URL + 'api/equipment/categories/page',
                dataTablesParameters
            )
            .pipe(
                switchMap((response: any) => {
                    return of(response);
                })
            );
    }

    // Equipment Category methods
    createCategory(data: any): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/categories', data);
    }

    updateCategory(id: number, data: any): Observable<any> {
        return this._http.put(environment.API_URL + 'api/equipment/categories/' + id, data);
    }

    getCategory(id: number): Observable<any> {
        return this._http.get(environment.API_URL + 'api/equipment/categories/' + id);
    }

    deleteCategory(id: number): Observable<any> {
        return this._http.delete(environment.API_URL + 'api/equipment/categories/' + id);
    }
}
