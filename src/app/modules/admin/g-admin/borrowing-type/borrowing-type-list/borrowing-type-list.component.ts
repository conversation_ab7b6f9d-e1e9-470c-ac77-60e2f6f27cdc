import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router, ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { AuthService } from 'app/core/auth/auth.service';
import { AssetType } from 'app/shared/asset-category';
import { environment } from 'environments/environment';
import { Subject, Observable } from 'rxjs';
import { DataWarehouse } from '../../advancemoney/advancemoney.types';
import { DialogCategoryFormComponent } from '../dialog-category-form/dialog-category-form.component';
import { CommonModule } from '@angular/common';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
import { BorrowingTypeService } from '../borrowing-type.service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';

@Component({
    selector: 'app-borrowing-type-list',
    imports: [
        DataTablesModule,
        CommonModule,
        BuddhistDatePipe,
        MatIconModule,
        MatButtonModule,
        MatMenuModule,
    ],
    templateUrl: './borrowing-type-list.component.html',
    standalone: true,
})
export class BorrowingTypeListComponent implements OnInit, AfterViewInit, OnDestroy {

    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    dtOptions: DataTables.Settings = {};

    dataRow: any[] = [];
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;

    formFieldHelpers: string[] = ['fuse-mat-dense'];
    status: { value: string, name: string }[] = [
        { value: null, name: 'ทั้งหมด' },
        { value: 'pending', name: 'รออนุมัติ' },
        { value: 'approved', name: 'อนุมัติ' },
        { value: 'borrowed', name: 'ยืมแล้ว' },
        { value: 'returned', name: 'คืนแล้ว' },
        { value: 'cancelled', name: 'ยกเลิก' },
        { value: 'overdue', name: 'เลยกำหนด' },
    ];

    /**
     * Constructor
     */
    constructor(
        private _fuseConfirmationService: FuseConfirmationService,
        private _Service: BorrowingTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        this.loadTable();
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };

    loadTable(): void {
        const that = this;

        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            order: [[1, 'desc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                that._Service
                    .getDataPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                    });
            },
            columns: [
                { data: 'action', orderable: false },
                { data: 'name' },
                { data: 'description' },
                { data: 'created_at' },
                { data: 'status' },
            ],
        };
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    New() {
        const dialogRef = this._matDialog.open(DialogCategoryFormComponent, {
            width: '600px',
            height: 'auto',
            disableClose: true
        });

        dialogRef.afterClosed().subscribe((result) => {
            if (result) {
                this.rerender();
            }
        });
    }

    Edit(categoryData: any) {
        this._Service.getCategory(categoryData.id).subscribe((resp: any) => {
            const dialogRef = this._matDialog.open(DialogCategoryFormComponent, {
                width: '600px',
                height: 'auto',
                disableClose: true,
                data: { categoryData: resp.data }
            });

            dialogRef.afterClosed().subscribe((result) => {
                if (result) {
                    this.rerender();
                }
            });
        });

    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id: number) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ลบประเภทอุปกรณ์ยืม',
            message: 'คุณต้องการลบประเภทอุปกรณ์ยืมใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .deleteCategory(id)
                    .subscribe({
                        next: () => {
                            this.rerender();
                        },
                        error: (err) => {
                            alert(err?.error?.message ?? 'ไม่สามารถลบประเภทอุปกรณ์ได้');
                        },
                    });
            }
        });
    }
}
