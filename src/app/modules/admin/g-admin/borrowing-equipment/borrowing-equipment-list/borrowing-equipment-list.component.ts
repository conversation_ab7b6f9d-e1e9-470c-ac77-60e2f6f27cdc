import { AfterViewInit, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Router, ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { AuthService } from 'app/core/auth/auth.service';
import { environment } from 'environments/environment';
import { Subject } from 'rxjs';
import { CommonModule } from '@angular/common';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { BorrowingEquipmentService } from '../borrowing-equipment.service';

@Component({
    selector: 'app-borrowing-equipment-list',
    imports: [
        DataTablesModule,
        CommonModule,
        BuddhistDatePipe,
        MatIconModule,
        MatButtonModule,
        MatMenuModule,
    ],
    templateUrl: './borrowing-equipment-list.component.html',
    standalone: true,
})
export class BorrowingEquipmentListComponent implements OnInit, AfterViewInit, OnDestroy {

    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    dtOptions: DataTables.Settings = {};

    dataRow: any[] = [];
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;

    formFieldHelpers: string[] = ['fuse-mat-dense'];

    /**
     * Constructor
     */
    constructor(
        private _fuseConfirmationService: FuseConfirmationService,
        private _Service: BorrowingEquipmentService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

        this.loadTable();
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };

    loadTable(): void {
        const that = this;

        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            order: [[1, 'desc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                that._Service
                    .getDataPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                    });
            },
            columns: [
                { data: 'action', orderable: false },      // ปุ่มจัดการ (edit/delete)
                { data: 'code' },                          // รหัส
                { data: 'name' },                          // ชื่อ
                { data: 'description' },                   // คำอธิบาย
                { data: 'brand' },                         // ยี่ห้อ
                { data: 'model' },                         // รุ่น
                { data: 'serial_number' },                 // Serial
                { data: 'purchase_price' },                // ราคา
                { data: 'purchase_date' },                 // วันที่ซื้อ
                { data: 'location' },                      // ตำแหน่ง
                { data: 'status' },                        // สถานะ (available)
                { data: 'created_at' }                     // วันที่สร้าง
            ]
        };
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    New() {
        // const dialogRef = this._matDialog.open(DialogCategoryFormComponent, {
        //     width: '600px',
        //     height: 'auto',
        //     disableClose: true
        // });

        // dialogRef.afterClosed().subscribe((result) => {
        //     if (result) {
        //         this.rerender();
        //     }
        // });
    }

    Edit(categoryData: any) {
        // this._Service.getCategory(categoryData.id).subscribe((resp: any) => {
        //     const dialogRef = this._matDialog.open(DialogCategoryFormComponent, {
        //         width: '600px',
        //         height: 'auto',
        //         disableClose: true,
        //         data: { categoryData: resp.data }
        //     });

        //     dialogRef.afterClosed().subscribe((result) => {
        //         if (result) {
        //             this.rerender();
        //         }
        //     });
        // });
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id: number) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ลบประเภทอุปกรณ์ยืม',
            message: 'คุณต้องการลบประเภทอุปกรณ์ยืมใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .deleteCategory(id)
                    .subscribe({
                        next: () => {
                            this.rerender();
                        },
                        error: (err) => {
                            alert(err?.error?.message ?? 'ไม่สามารถลบประเภทอุปกรณ์ได้');
                        },
                    });
            }
        });
    }
}
