<div class="flex flex-col max-h-screen -m-6">
  <!-- Header -->
  <div class="flex flex-0 items-center justify-between h-16 pr-3 sm:pr-5 pl-6 sm:pl-8 bg-primary text-on-primary">
    <div class="text-lg font-medium">
      {{ isEditMode ? 'แก้ไขประเภทอุปกรณ์ยืม' : 'สร้างประเภทอุปกรณ์ยืม' }}
    </div>
    <button mat-icon-button (click)="onCancel()" [tabIndex]="-1">
      <mat-icon class="text-current" [svgIcon]="'heroicons_outline:x-mark'"></mat-icon>
    </button>
  </div>

  <!-- Content -->
  <form [formGroup]="categoryForm" class="flex flex-col flex-auto p-6 sm:p-8">

    <!-- Name Field -->
    <mat-form-field class="mb-4">
      <mat-label>ชื่อประเภทอุปกรณ์</mat-label>
      <input matInput formControlName="name" required
             placeholder="เช่น อุปกรณ์เสียง">
      <mat-error *ngIf="categoryForm.get('name')?.hasError('required')">
        กรุณาระบุชื่อประเภทอุปกรณ์
      </mat-error>
    </mat-form-field>

    <!-- Description Field -->
    <mat-form-field class="mb-4">
      <mat-label>คำอธิบาย</mat-label>
      <textarea matInput formControlName="description" rows="3" required
                placeholder="เช่น อุปกรณ์เสียงและเครื่องขยายเสียง"></textarea>
      <mat-error *ngIf="categoryForm.get('description')?.hasError('required')">
        กรุณาระบุคำอธิบาย
      </mat-error>
    </mat-form-field>

    <!-- Status Field -->
    <div class="mb-6">
      <mat-checkbox formControlName="status">
        เปิดใช้งาน
      </mat-checkbox>
    </div>

    <!-- Actions -->
    <div class="flex items-center justify-end mt-8 space-x-4">
      <button mat-stroked-button type="button" (click)="onCancel()">
        ยกเลิก
      </button>
      <button mat-raised-button color="primary" type="submit"
              [disabled]="categoryForm.invalid || isLoading"
              (click)="onSubmit()">
        <mat-icon *ngIf="isLoading" class="animate-spin">refresh</mat-icon>
        {{ isEditMode ? 'บันทึกการแก้ไข' : 'สร้างประเภทอุปกรณ์' }}
      </button>
    </div>

  </form>
</div>
