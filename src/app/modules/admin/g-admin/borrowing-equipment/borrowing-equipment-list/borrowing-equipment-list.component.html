<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">อุปกรณ์</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <button class="ml-4 bg-green-500" mat-flat-button (click)="New()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">สร้างอุปกรณ์</span>
            </button>
        </div>
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">

                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden whitespace-nowrap">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="text-center">จัดการ</th>
                                <th>รหัส</th>
                                <th>ชื่อ</th>
                                <th>คำอธิบาย</th>
                                <th>ยี่ห้อ</th>
                                <th>รุ่น</th>
                                <th>Serial</th>
                                <th>ราคา</th>
                                <th>วันที่ซื้อ</th>
                                <th>สถานที่</th>
                                <th class="text-center">สถานะ</th>
                                <th>วันที่สร้าง</th>
                            </tr>
                        </thead>

                        <!-- กรณีมีข้อมูล -->
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                <!-- จัดการ -->
                                <td class="text-center">
                                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" matTooltip="ตัวเลือก">
                                        <mat-icon>more_vert</mat-icon>
                                    </button>
                                    <mat-menu #actionMenu="matMenu">
                                        <button mat-menu-item (click)="Edit(item)">
                                            <mat-icon class="text-blue-500">edit</mat-icon>
                                            <span>แก้ไข</span>
                                        </button>
                                        <button mat-menu-item (click)="Delete(item.id)">
                                            <mat-icon class="text-red-500">delete</mat-icon>
                                            <span>ลบ</span>
                                        </button>
                                    </mat-menu>
                                </td>

                                <!-- รายละเอียด -->
                                <td>{{ item?.code ?? '' }}</td>
                                <td>{{ item?.name ?? '' }}</td>
                                <td>{{ item?.description ?? '' }}</td>
                                <td>{{ item?.brand ?? '' }}</td>
                                <td>{{ item?.model ?? '' }}</td>
                                <td>{{ item?.serial_number ?? '' }}</td>
                                <td>{{ item?.purchase_price | number:'1.2-2' }}</td>
                                <td>{{ item?.purchase_date | buddhistDate:'dd/MM/yyyy' ?? '' }}</td>
                                <td>{{ item?.location ?? '' }}</td>

                                <!-- สถานะ -->
                                <td class="text-center">
                                    <div class="text-green-500 font-semibold px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.is_active">
                                        เปิดใช้งาน
                                    </div>
                                    <div class="text-red-500 font-semibold px-2.5 py-0.5 rounded dark:bg-red-200 dark:text-red-900"
                                        *ngIf="!item.is_active">
                                        ปิดใช้งาน
                                    </div>
                                </td>

                                <!-- วันที่สร้าง -->
                                <td>{{ item?.created_at | buddhistDate:'dd/MM/yyyy HH:mm' ?? '' }}</td>
                            </tr>
                        </tbody>

                        <!-- กรณีไม่มีข้อมูล -->
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="12" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
