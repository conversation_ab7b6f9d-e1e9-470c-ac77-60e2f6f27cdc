import { Route } from '@angular/router';
import { AuthGuard } from 'app/core/auth/guards/auth.guard';
import { NoAuthGuard } from 'app/core/auth/guards/noAuth.guard';
import { InitialDataResolver } from 'app/app.resolvers';
import { LayoutComponent } from 'app/layout/layout.component';

// @formatter:off
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
export const appRoutes: Route[] = [
    // Redirect empty path to '/dashboards/project'
    { path: '', pathMatch: 'full', redirectTo: 'home/list' },

    // Redirect signed in user to the '/dashboards/project'
    //
    // After the user signs in, the sign in page will redirect the user to the 'signed-in-redirect'
    // path. Below is another redirection for that path to redirect the user to the desired
    // location. This is a small convenience to keep all main routes together here on this file.
    { path: 'signed-in-redirect', pathMatch: 'full', redirectTo: 'home/list' },

    // Auth routes for guests
    {
        path: '',
        canActivate: [NoAuthGuard],
        canActivateChild: [NoAuthGuard],
        component: LayoutComponent,
        data: {
            layout: 'empty',
        },
        children: [
            { path: 'confirmation-required', loadChildren: () => import('app/modules/auth/confirmation-required/confirmation-required.routing') },
            { path: 'forgot-password', loadChildren: () => import('app/modules/auth/forgot-password/forgot-password.routing') },
            { path: 'reset-password', loadChildren: () => import('app/modules/auth/reset-password/reset-password.routing') },
            { path: 'sign-in', loadChildren: () => import('app/modules/auth/sign-in/sign-in.routing') },
            { path: 'line', loadChildren: () => import('app/modules/admin/line/line.routing') },
            { path: 'sign-up', loadChildren: () => import('app/modules/auth/sign-up/sign-up.routing') },
            { path: 'check-in', loadChildren: () => import('app/modules/admin/line/check-in/check-in.routing') },
            { path: 'get-leave', loadComponent: () => import('app/modules/admin/line/line-leave/line-leave.component').then(m => m.LineLeaveComponent) },
            { path: 'get-ot', loadComponent: () => import('app/modules/admin/line/line-ot/line-ot.component').then(m => m.LineOtComponent) },
            { path: 'leave-approve', loadComponent: () => import('app/modules/admin/line/leave-approve/leave-approve.component').then(m => m.LeaveApproveComponent) },
            { path: 'leave-list', loadComponent: () => import('app/modules/admin/line/leave-list/leave-list.component').then(m => m.LeaveListComponent) },
            { path: 'ot-approve', loadComponent: () => import('app/modules/admin/line/ot-approve/ot-approve.component').then(m => m.OtApproveComponent) },
            { path: 'ot-list', loadComponent: () => import('app/modules/admin/line/ot-list/ot-list.component').then(m => m.OtListLineComponent) },
            { path: 'profile-line', loadComponent: () => import('app/modules/admin/line/profile/profile.component').then(m => m.ProfileLineComponent) },
            { path: 'warning-approve', loadComponent: () => import('app/modules/admin/line/warning-approve/warning.component').then(m => m.WarningApproveComponent) },
            { path: 'warning-list', loadComponent: () => import('app/modules/admin/line/warning-list/warning-list.component').then(m => m.WarningListLineComponent) },
            { path: 'warning-view', loadComponent: () => import('app/modules/admin/line/warning-view/warning-list.component').then(m => m.WarningViewLineComponent) },
        ],
    },
    // Auth routes for authenticated users
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        loadComponent: () => import('app/layout/layout.component').then(m => m.LayoutComponent),
        data: {
            layout: 'empty',
        },
        children: [
            { path: 'sign-out', loadChildren: () => import('app/modules/auth/sign-out/sign-out.routing') },
            { path: 'unlock-session', loadChildren: () => import('app/modules/auth/unlock-session/unlock-session.routing') },
        ],
    },

    // Admin routes
    {
        path: '',
        canActivate: [AuthGuard],
        canActivateChild: [AuthGuard],
        loadComponent: () => import('app/layout/layout.component').then(m => m.LayoutComponent),
        resolve: {
            initialData: InitialDataResolver,
        },
        children: [
            { path: '', loadComponent: () => import('app/modules/landing/landing.component').then((m) => m.LandingComponent) },
            { path: 'general', loadChildren: () => import('app/modules/admin/g-admin/general/general.routing') },
            { path: 'home', loadChildren: () => import('app/modules/admin/pages/home/<USER>') },
            //permission
            { path: 'permission', loadChildren: () => import('app/modules/admin/g-admin/permission/permission.routing') },
            { path: 'table', loadChildren: () => import('app/modules/admin/g-admin/table/table.routing') },
            // { path: 'orders', loadComponent: () => import('app/modules/admin/marketing/orders/orders.module').then(m => m.OrdersModule) },
            // { path: 'expand-store-list', loadComponent: () => import('app/modules/admin/marketing/orders/expand-store-list/expand-store-list.module').then(m => m.ExpandStoreModule) },
            // { //     path: 'data', children: [
            //         { path: 'new-item-list-checking', loadComponent: () => import('app/modules/admin/marketing/new-item-list-checking/new-item-list-checking.module').then(m => m.NewItemListCheckingModule) },
            //         { path: 'assets-list', loadComponent: () => import('app/modules/admin/marketing/assets-list/assets-list.module').then(m => m.AssetsListModule) },
            //         { path: 'user', loadComponent: () => import('app/modules/admin/marketing/user/user.module').then(m => m.UserListModule) },
            //         { path: 'store', loadComponent: () => import('app/modules/admin/marketing/store/store.module').then(m => m.StoreModule) },
            //         { path: 'store-type', loadComponent: () => import('app/modules/admin/marketing/store-type/store-type.module').then(m => m.StoreTypeModule) },
            //     ]
            // }, ], },
            //user
            { path: 'print-doc',           loadChildren: () => import('app/modules/admin/g-admin/print-doc/print-doc.routing') },
            { path: 'check-in-location', loadChildren: () => import('app/modules/admin/g-admin/checkInarea/area.routing') },
            { path: 'user', loadChildren: () => import('app/modules/admin/g-admin/user/user.routing') },
            { path: 'branch', loadChildren: () => import('app/modules/admin/g-admin/branch/branch.routing') },
            { path: 'department', loadChildren: () => import('app/modules/admin/g-admin/department/department.routing') },
            { path: 'position', loadChildren: () => import('app/modules/admin/g-admin/position/position.routing') },
            { path: 'bank', loadChildren: () => import('app/modules/admin/g-admin/bank/bank.routing') },
            { path: 'page', loadChildren: () => import('app/modules/admin/g-admin/page/page.routing') },
            //**กำหนด path */ส่วนที่ต้องทำ/เพิ่มนที่ต้องทำ/เพิ่ม
            { path: 'save-sound', loadChildren: () => import('app/modules/admin/g-admin/save-sound/save-sound.routing'), },
            { path: 'delivery', loadChildren: () => import('app/modules/admin/g-admin/delivery/delivery.routing') },
            { path: 'channel', loadChildren: () => import('app/modules/admin/g-admin/channel/channel.routing') },
            { path: 'config', loadChildren: () => import('app/modules/admin/g-admin/config/config.routing') },
            { path: 'bonus-step', loadChildren: () => import('app/modules/admin/g-admin/bonus-step/bonus-step.routing') },
            { path: 'advancemoney', loadChildren: () => import('app/modules/admin/g-admin/advancemoney/advancemoney.routing') },
            { path: 'customer', loadChildren: () => import('app/modules/admin/g-admin/customer/customer.routing') },
            { path: 'worktelesale', loadChildren: () => import('app/modules/admin/g-admin/worktelesale/worktelesale.routing'), },
            { path: 'workads', loadChildren: () => import('app/modules/admin/g-admin/workads/workads.routing') },
            { path: 'workadmin', loadChildren: () => import('app/modules/admin/g-admin/workadmin/workadmin.routing') },
            { path: 'worktime', loadChildren: () => import('app/modules/admin/g-admin/worktime/worktime.routing') },
            { path: 'worktime-monthly', loadChildren: () => import('app/modules/admin/g-admin/worktime-monthly/worktime-monthly.routing') },
            { path: 'item-type', loadChildren: () => import('app/modules/admin/g-admin/item-type/item-type.routing') },
            { path: 'leave-list', loadChildren: () => import('app/modules/admin/g-admin/leave-list/leave-list.routing') },
            { path: 'leave-type', loadChildren: () => import('app/modules/admin/g-admin/leave-type/leave-type.routing') },
            { path: 'item-return', loadChildren: () => import('app/modules/admin/g-admin/item-return/item-return.routing') },
            { path: 'ot', loadChildren: () => import('app/modules/admin/g-admin/ot/ot.routing') },
            { path: 'ot-type', loadChildren: () => import('app/modules/admin/g-admin/ot-type/page.routing') },
            { path: 'yellow-card', loadChildren: () => import('app/modules/admin/g-admin/yellow-card/page.routing') },
            { path: 'salary', loadChildren: () => import('app/modules/admin/g-admin/salary/salary.routing') },
            { path: 'commission', loadChildren: () => import('app/modules/admin/g-admin/commission/commission.routing') },
            { path: 'plusmoney', loadChildren: () => import('app/modules/admin/g-admin/plusmoney/plusmoney.routing') },
            { path: 'plusmoney-type', loadChildren: () => import('app/modules/admin/g-admin/plusmoney-type/plusmoney-type.routing') },
            { path: 'deletemoney', loadChildren: () => import('app/modules/admin/g-admin/deletemoney/deletemoney.routing') },
            { path: 'deletemoney-type', loadChildren: () => import('app/modules/admin/g-admin/deletemoney-type/deletemoney-type.routing') },
            { path: 'location', loadChildren: () => import('app/modules/admin/g-admin/location/location.routing') },
            { path: 'warehouse', loadChildren: () => import('app/modules/admin/g-admin/warehouse/warehouse.routing') },
            { path: 'vendor', loadChildren: () => import('app/modules/admin/g-admin/vendor/vendor.routing') },
            { path: 'item', loadChildren: () => import('app/modules/admin/g-admin/item/item.routing') },
            {
                path: 'stock', children: [
                    { path: 'deposit', loadChildren: () => import('app/modules/admin/g-admin/stock/deposit/deposit.routing') },
                    { path: 'withdraw', loadChildren: () => import('app/modules/admin/g-admin/stock/withdraw/withdraw.routing') },
                ],
            },
            { path: 'sale-order', loadChildren: () => import('app/modules/admin/g-admin/sale-order/saleorder.routing') },
            { path: 'sale-page', loadChildren: () => import('app/modules/admin/g-admin/sale-page/sale-page.routing') },
            { path: 'time-attendance', loadChildren: () => import('app/modules/admin/g-admin/time-attendance/time-attendance.routing') },
            { path: 'admin-report', loadChildren: () => import('app/modules/admin/g-admin/admin-report/admin-report.routing') },
            { path: 'ads-report', loadChildren: () => import('app/modules/admin/g-admin/ads-report/ads-report.routing') },
            { path: 'tiktok-report', loadChildren: () => import('app/modules/admin/g-admin/ads-report/ads-report.routing') },
            { path: 'leave', loadChildren: () => import('app/modules/admin/g-admin/leave/leave.routing') },
            { path: 'check-status', loadChildren: () => import('app/modules/admin/pages/check-status/check-status.routing') },
            { path: 'calendar', loadChildren: () => import('app/modules/admin/g-admin/calendar/calendar.routing') },
            { path: 'chief', loadChildren: () => import('app/modules/admin/g-admin/chief-telesale/chief-telesale.routing') },
            { path: 'customervip', loadChildren: () => import('app/modules/admin/g-admin/customerVIP/customervip.routing') },
            { path: 'holiday', loadChildren: () => import('app/modules/admin/g-admin/holiday/holiday.routing') },
            { path: 'holidaysetting', loadChildren: () => import('app/modules/admin/g-admin/holiday-setting/holiday-setting.routing') },
            { path: 'assign', loadChildren: () => import('app/modules/admin/g-admin/assign-telesale/assign-telesale.routing') },
            { path: 'follow', loadChildren: () => import('app/modules/admin/g-admin/follow-telesale/follow-telesale.routing') },
            { path: 'reorder', loadChildren: () => import('app/modules/admin/g-admin/reorder-telesale/reorder-telesale.routing') },
            { path: 'refund', loadChildren: () => import('app/modules/admin/g-admin/refund-telesale/refund-telesale.routing') },
            { path: 'po', loadChildren: () => import('app/modules/admin/g-admin/po/page.routing') },
            { path: 'manange_warning', loadChildren: () => import('app/modules/admin/g-admin/manage-warning/manage-warning.routing') },
            { path: 'report_warning', loadChildren: () => import('app/modules/admin/g-admin/report-warning/report-warning.routing') },
            { path: 'employee_deposit', loadChildren: () => import('app/modules/admin/g-admin/employee-deposit/employee-deposit.routing') },
            {
                path: 'report', children: [
                    { path: 'dead-stock', loadChildren: () => import('app/modules/admin/g-admin/report/dead-stock/dead-stock.routing') },
                    { path: 'slow-stock', loadChildren: () => import('app/modules/admin/g-admin/report/slow-stock/slow-stock.routing') },
                    { path: 'stock-item', loadChildren: () => import('app/modules/admin/g-admin/report/stock-item/stock-item.routing') },
                    { path: 'item-location', loadChildren: () => import('app/modules/admin/g-admin/report/item-location/item-location.routing') },
                    { path: 'item-type', loadChildren: () => import('app/modules/admin/g-admin/report/item-type/item-type-report.routing') },
                    { path: 'item', loadChildren: () => import('app/modules/admin/g-admin/report/item/item-report.routing') },
                    { path: 'sale-order', loadChildren: () => import('app/modules/admin/g-admin/report/sale-order/sale-order.routing'), },
                    { path: 'time-attendance', loadChildren: () => import('app/modules/admin/g-admin/report/time-attendance/time-attendance.routing') },
                ]
            },
            { path: 'company', loadChildren: () => import('app/modules/admin/g-admin/company/page.routing') },
            { path: 'location', loadChildren: () => import('app/modules/admin/g-admin/site/page.routing') },
            { path: 'payroll-contribution-setting', loadChildren: () => import('app/modules/admin/g-admin/payroll-setting/page.routing') },
            // 404 & Catch all
            {
                path: '404-not-found',
                pathMatch: 'full',
                loadChildren: () =>
                    import('app/modules/admin/pages/error/error-404/error-404.routing'),
            },

            { path: 'borrowing', loadChildren: () => import('app/modules/admin/g-admin/borrowing/page.routing') },
            { path: 'borrowing-type', loadChildren: () => import('app/modules/admin/g-admin/borrowing-type/page.routing') },
            { path: 'borrowing-equipment', loadChildren: () => import('app/modules/admin/g-admin/borrowing-equipment/page.routing') },

            { path: '**', redirectTo: '404-not-found' },
        ]
    }
]
