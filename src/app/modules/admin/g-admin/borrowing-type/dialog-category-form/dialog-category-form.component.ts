import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { BorrowingTypeService } from '../borrowing-type.service';

@Component({
  selector: 'app-dialog-category-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCheckboxModule,
  ],
  templateUrl: './dialog-category-form.component.html',
  styleUrls: ['./dialog-category-form.component.scss']
})
export class DialogCategoryFormComponent implements OnInit {
  categoryForm: FormGroup;
  isLoading = false;
  isEditMode = false;

  constructor(
    public dialogRef: MatDialogRef<DialogCategoryFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private service: BorrowingTypeService,
    private fuseConfirmationService: FuseConfirmationService,
  ) {
    this.isEditMode = !!data?.categoryData;
    this.initForm();
  }

  ngOnInit(): void {
    if (this.isEditMode) {
      this.populateForm();
    }
  }

  initForm(): void {
    this.categoryForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      status: [true]
    });
  }

  populateForm(): void {
    if (this.data.categoryData) {
      const data = this.data.categoryData;
      this.categoryForm.patchValue({
        name: data.name,
        description: data.description,
        status: data.status
      });
    }
  }

  onSubmit(): void {
    if (this.categoryForm.valid) {
      this.isLoading = true;

      const formData = this.categoryForm.value;

      const request = this.isEditMode
        ? this.service.updateCategory(this.data.categoryData.id, formData)
        : this.service.createCategory(formData);

      request.subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccessMessage();
          this.dialogRef.close(response);
        },
        error: (error) => {
          this.isLoading = false;
          this.showErrorMessage(error.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล');
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  markFormGroupTouched(): void {
    Object.keys(this.categoryForm.controls).forEach(key => {
      this.categoryForm.get(key)?.markAsTouched();
    });
  }

  showSuccessMessage(): void {
    this.fuseConfirmationService.open({
      title: 'สำเร็จ',
      message: this.isEditMode ? 'แก้ไขประเภทอุปกรณ์ยืมเรียบร้อย' : 'สร้างประเภทอุปกรณ์ยืมเรียบร้อย',
      icon: {
        show: true,
        name: 'heroicons_outline:check-circle',
        color: 'success'
      },
      actions: {
        confirm: {
          show: false
        },
        cancel: {
          show: false
        }
      },
      dismissible: true
    });
  }

  showErrorMessage(message: string): void {
    this.fuseConfirmationService.open({
      title: 'เกิดข้อผิดพลาด',
      message: message,
      icon: {
        show: true,
        name: 'heroicons_outline:exclamation-triangle',
        color: 'warn'
      },
      actions: {
        confirm: {
          show: false
        },
        cancel: {
          show: false
        }
      },
      dismissible: true
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
